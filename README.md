# Enable Standard PayPal for WooCommerce
* Contributors: vikche<PERSON>
* Donate link: https://paypal.me/SukhwantCheema
* Tags: woocommerce, payment gateway, paypal payment method, paypal standard, standard paypal method
* WordPress Tested up to: 6.8.1
* WC requires at least: 5.5.0
* WC tested up to: 9.8.3
* Stable tag: 1.0.2
* License: GNU General Public License v3.0
* License URI: https://www.gnu.org/licenses/gpl-3.0.html

Enables the classic PayPal Standard payment method for WooCommerce, which has been disabled by default since WooCommerce version 5.5.0.

== Description ==

**Enables the classic PayPal Standard payment method for WooCommerce, which has been disabled by default since WooCommerce version 5.5.0.

== Installation ==

1. Download and install zip file under Plugins -> New or Upload the entire plugin folder to the '/wp-content/plugins/' directory
2. Activate the plugin through the "Plugins" menu in WordPress.
3. That's it!
4. Go to Woocoomerce -> Settings -> Payments and you will see the Standard PayPal method visible.

Just install and activate the plugin. No settings required.

== Screenshots ==

1. Go to Woocommerce -> Settings -> Payments
<img width="1536" alt="woocoomerce-settings-payments" src="https://user-images.githubusercontent.com/4162718/132106577-edf2cc5f-71e8-4d58-8f3e-b1663c506f09.png">

== Changelog ==

= 1.0.2 =
* Tags and short description fix

= 1.0.1 =
* Added - Compatibility test upto WooCommerce 9.8.3 
* Added - Compatibility test upto Wordpress 6.8.1

= 1.0.0 =
* Initial Release.
